/**
 * 用户认证管理模块
 * 处理登录、注册、token管理等功能
 */

class AuthManager {
    constructor() {
        this.token = localStorage.getItem('auth_token');
        this.user = JSON.parse(localStorage.getItem('user_info') || 'null');
        this.apiBase = '/api';
        this.initialized = false;
    }

    /**
     * 初始化认证管理器
     */
    init() {
        // 防止重复初始化
        if (this.initialized) {
            console.log('认证管理器已初始化，跳过');
            return;
        }

        // 检查当前页面
        const currentPath = window.location.pathname;

        console.log('初始化认证管理器 - 当前路径:', currentPath);
        console.log('认证状态:', this.isAuthenticated());

        if (currentPath === '/' || currentPath === '/login.html') {
            // 登录页面
            this.initLoginPage();
        } else if (currentPath === '/app') {
            // 应用主页面
            this.checkAuthAndRedirect();
        } else {
            // 其他页面，检查认证状态
            if (!this.isAuthenticated()) {
                window.location.href = '/';
                return;
            }
        }

        this.initialized = true;
    }

    /**
     * 初始化登录页面
     */
    initLoginPage() {
        // 如果已登录，验证token有效性后再跳转
        if (this.isAuthenticated()) {
            this.verifyTokenAndRedirect();
            return;
        }

        // 确保页面元素存在后再初始化
        if (document.getElementById('loginFormElement')) {
            this.initLoginForm();
            this.initRegisterForm();
            this.initFormToggle();
            this.initPasswordToggle();
        } else {
            console.error('登录页面元素未找到');
        }
    }

    /**
     * 验证token并重定向
     */
    async verifyTokenAndRedirect() {
        try {
            const response = await fetch(`${this.apiBase}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.user = data.user;
                localStorage.setItem('user_info', JSON.stringify(this.user));
                window.location.href = '/app';
            } else {
                // Token无效，清除认证信息
                this.logout();
            }
        } catch (error) {
            console.error('Token验证失败:', error);
            this.logout();
        }
    }

    /**
     * 检查认证状态并重定向
     */
    async checkAuthAndRedirect() {
        if (!this.isAuthenticated()) {
            window.location.href = '/';
            return;
        }

        try {
            // 验证token是否有效
            const response = await fetch(`${this.apiBase}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (!response.ok) {
                throw new Error('Token验证失败');
            }

            const data = await response.json();
            this.user = data.user;
            localStorage.setItem('user_info', JSON.stringify(this.user));
            
            // 初始化应用界面
            this.initAppInterface();
        } catch (error) {
            console.error('认证验证失败:', error);
            this.logout();
        }
    }

    /**
     * 初始化登录表单
     */
    initLoginForm() {
        const form = document.getElementById('loginFormElement');
        const togglePassword = document.getElementById('toggleLoginPassword');

        if (!form || !togglePassword) {
            console.error('登录表单元素未找到');
            return;
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleLogin(new FormData(form));
        });

        // 密码显示切换
        togglePassword.addEventListener('click', () => {
            const passwordInput = document.getElementById('loginPassword');
            const icon = togglePassword.querySelector('i');

            if (passwordInput && icon) {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    passwordInput.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            }
        });
    }

    /**
     * 初始化注册表单
     */
    initRegisterForm() {
        const form = document.getElementById('registerFormElement');
        const togglePassword = document.getElementById('toggleRegisterPassword');
        const confirmPassword = document.getElementById('confirmPassword');

        if (!form || !togglePassword || !confirmPassword) {
            console.error('注册表单元素未找到');
            return;
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleRegister(new FormData(form));
        });

        // 密码显示切换
        togglePassword.addEventListener('click', () => {
            const passwordInput = document.getElementById('registerPassword');
            const icon = togglePassword.querySelector('i');

            if (passwordInput && icon) {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    passwordInput.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            }
        });

        // 密码确认验证
        confirmPassword.addEventListener('input', () => {
            const password = document.getElementById('registerPassword');
            if (password && confirmPassword.value && confirmPassword.value !== password.value) {
                confirmPassword.setCustomValidity('密码不匹配');
            } else {
                confirmPassword.setCustomValidity('');
            }
        });
    }

    /**
     * 初始化表单切换
     */
    initFormToggle() {
        const showRegisterBtn = document.getElementById('showRegisterBtn');
        const showLoginBtn = document.getElementById('showLoginBtn');
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        if (!showRegisterBtn || !showLoginBtn || !loginForm || !registerForm) {
            console.error('表单切换元素未找到');
            return;
        }

        showRegisterBtn.addEventListener('click', () => {
            loginForm.classList.add('hidden');
            registerForm.classList.remove('hidden');
        });

        showLoginBtn.addEventListener('click', () => {
            registerForm.classList.add('hidden');
            loginForm.classList.remove('hidden');
        });
    }

    /**
     * 初始化密码切换功能
     */
    initPasswordToggle() {
        // 已在各自的表单初始化中处理
    }

    /**
     * 处理登录
     */
    async handleLogin(formData) {
        const loginBtn = document.getElementById('loginBtn');
        const loginBtnText = document.getElementById('loginBtnText');
        const loginBtnLoading = document.getElementById('loginBtnLoading');

        try {
            // 显示加载状态
            loginBtn.disabled = true;
            loginBtnText.classList.add('hidden');
            loginBtnLoading.classList.remove('hidden');

            const response = await fetch(`${this.apiBase}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: formData.get('username'),
                    password: formData.get('password')
                })
            });

            const data = await response.json();

            if (response.ok) {
                // 登录成功
                this.token = data.token;
                this.user = data.user;
                
                localStorage.setItem('auth_token', this.token);
                localStorage.setItem('user_info', JSON.stringify(this.user));
                
                this.showMessage('登录成功！正在跳转...', 'success');
                
                setTimeout(() => {
                    window.location.href = '/app';
                }, 1000);
            } else {
                throw new Error(data.error || '登录失败');
            }
        } catch (error) {
            console.error('登录失败:', error);
            this.showMessage(error.message, 'error');
        } finally {
            // 恢复按钮状态
            loginBtn.disabled = false;
            loginBtnText.classList.remove('hidden');
            loginBtnLoading.classList.add('hidden');
        }
    }

    /**
     * 处理注册
     */
    async handleRegister(formData) {
        const registerBtn = document.getElementById('registerBtn');
        const registerBtnText = document.getElementById('registerBtnText');
        const registerBtnLoading = document.getElementById('registerBtnLoading');

        try {
            // 验证密码匹配
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            
            if (password !== confirmPassword) {
                throw new Error('密码不匹配');
            }

            // 显示加载状态
            registerBtn.disabled = true;
            registerBtnText.classList.add('hidden');
            registerBtnLoading.classList.remove('hidden');

            const response = await fetch(`${this.apiBase}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: formData.get('username'),
                    email: formData.get('email'),
                    password: formData.get('password')
                })
            });

            const data = await response.json();

            if (response.ok) {
                // 注册成功，自动登录
                this.token = data.token;
                this.user = data.user;
                
                localStorage.setItem('auth_token', this.token);
                localStorage.setItem('user_info', JSON.stringify(this.user));
                
                this.showMessage('注册成功！正在跳转...', 'success');
                
                setTimeout(() => {
                    window.location.href = '/app';
                }, 1000);
            } else {
                throw new Error(data.error || '注册失败');
            }
        } catch (error) {
            console.error('注册失败:', error);
            this.showMessage(error.message, 'error');
        } finally {
            // 恢复按钮状态
            registerBtn.disabled = false;
            registerBtnText.classList.remove('hidden');
            registerBtnLoading.classList.add('hidden');
        }
    }

    /**
     * 初始化应用界面
     */
    initAppInterface() {
        // 更新用户信息显示
        this.updateUserInterface();
        
        // 初始化API模块
        if (window.apiManager) {
            window.apiManager.setToken(this.token);
        }
    }

    /**
     * 更新用户界面
     */
    updateUserInterface() {
        // 更新导航栏用户信息
        const userInfo = document.getElementById('userInfo');
        if (userInfo && this.user) {
            userInfo.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-user-circle text-xl"></i>
                        <span>${this.user.username}</span>
                        ${this.user.role === 'admin' ? '<span class="bg-red-500 text-white px-2 py-1 rounded text-xs">管理员</span>' : ''}
                    </div>
                    <button id="logoutBtn" class="hover:bg-white hover:bg-opacity-20 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>退出
                    </button>
                </div>
            `;

            // 绑定退出按钮
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', () => this.logout());
            }
        }

        // 显示/隐藏管理员功能
        const adminElements = document.querySelectorAll('.admin-only');
        adminElements.forEach(element => {
            if (this.user && this.user.role === 'admin') {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        });
    }

    /**
     * 检查是否已认证
     */
    isAuthenticated() {
        return !!(this.token && this.user);
    }

    /**
     * 检查是否是管理员
     */
    isAdmin() {
        return this.user && this.user.role === 'admin';
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }

    /**
     * 退出登录
     */
    logout() {
        console.log('执行退出登录');

        // 清除所有认证信息
        this.token = null;
        this.user = null;
        this.initialized = false; // 重置初始化标志
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');

        // 清除API管理器的token
        if (window.apiManager) {
            window.apiManager.setToken(null);
        }

        // 显示退出消息
        this.showMessage('已退出登录', 'info');

        // 延迟跳转，避免立即重定向
        setTimeout(() => {
            window.location.href = '/';
        }, 500);
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        if (!container) return;

        const messageEl = document.createElement('div');
        messageEl.className = `
            mb-3 p-4 rounded-lg shadow-lg max-w-sm fade-in
            ${type === 'success' ? 'bg-green-500 text-white' : 
              type === 'error' ? 'bg-red-500 text-white' : 
              type === 'warning' ? 'bg-yellow-500 text-white' : 
              'bg-blue-500 text-white'}
        `;
        
        messageEl.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button class="ml-3 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 添加关闭功能
        const closeBtn = messageEl.querySelector('button');
        closeBtn.addEventListener('click', () => {
            messageEl.remove();
        });

        container.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }
}

// 创建全局实例
window.authManager = new AuthManager();
