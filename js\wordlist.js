/**
 * 词表管理模块
 * 处理词表的创建、编辑、删除、导入导出等功能
 */

class WordlistManager {
    constructor() {
        this.currentWordlist = null;
        this.initEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 词表操作按钮
        document.getElementById('saveWordlistBtn').addEventListener('click', () => this.saveWordlist());
        document.getElementById('newWordlistBtn').addEventListener('click', () => this.newWordlist());
        document.getElementById('deleteWordlistBtn').addEventListener('click', () => this.deleteWordlist());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportWordlists());
        document.getElementById('importBtn').addEventListener('click', () => this.importWordlists());
        
        // 词表选择
        document.getElementById('wordlistSelect').addEventListener('change', (e) => this.loadWordlist(e.target.value));
        
        // 文件导入
        document.getElementById('importFile').addEventListener('change', (e) => this.handleFileImport(e));
        
        // 内容变化监听
        document.getElementById('wordlistContent').addEventListener('input', () => this.updateWordCount());
    }

    /**
     * 解析单词列表文本
     */
    parseWordlist(text) {
        const lines = text.trim().split('\n');
        const words = [];
        
        for (let line of lines) {
            line = line.trim();
            if (!line) continue;
            
            let chinese, english;
            
            // 支持多种分隔符
            if (line.includes(',')) {
                [chinese, english] = line.split(',').map(s => s.trim());
            } else if (line.includes('\t')) {
                [chinese, english] = line.split('\t').map(s => s.trim());
            } else {
                const spaceIndex = line.indexOf(' ');
                if (spaceIndex > 0) {
                    chinese = line.slice(0, spaceIndex).trim();
                    english = line.slice(spaceIndex + 1).trim();
                }
            }
            
            if (chinese && english) {
                words.push({ chinese, english });
            }
        }
        
        return words;
    }

    /**
     * 保存词表
     */
    async saveWordlist() {
        const name = document.getElementById('wordlistName').value.trim();
        const content = document.getElementById('wordlistContent').value.trim();
        
        if (!name) {
            this.showMessage('请输入词表名称', 'error');
            return;
        }
        
        if (!content) {
            this.showMessage('请输入单词内容', 'error');
            return;
        }
        
        const words = this.parseWordlist(content);
        if (words.length === 0) {
            this.showMessage('请输入有效的单词格式', 'error');
            return;
        }
        
        try {
            await wordDB.saveWordlist(name, words);
            this.showMessage(`词表 "${name}" 保存成功，共 ${words.length} 个单词`, 'success');
            await this.refreshWordlistSelect();
            this.updateWordlistInfo(name, words);
        } catch (error) {
            console.error('保存词表失败:', error);
            this.showMessage('保存失败，请重试', 'error');
        }
    }

    /**
     * 新建词表
     */
    newWordlist() {
        document.getElementById('wordlistName').value = '';
        document.getElementById('wordlistContent').value = '';
        document.getElementById('wordlistSelect').value = '';
        this.hideWordlistInfo();
        this.currentWordlist = null;
    }

    /**
     * 加载词表
     */
    async loadWordlist(name) {
        if (!name) {
            this.newWordlist();
            return;
        }
        
        try {
            const wordlist = await wordDB.getWordlistByName(name);
            if (wordlist) {
                document.getElementById('wordlistName').value = wordlist.name;
                
                // 将单词数组转换为文本格式
                const content = wordlist.words.map(word => `${word.chinese} ${word.english}`).join('\n');
                document.getElementById('wordlistContent').value = content;
                
                this.updateWordlistInfo(wordlist.name, wordlist.words);
                this.currentWordlist = wordlist;
            }
        } catch (error) {
            console.error('加载词表失败:', error);
            this.showMessage('加载词表失败', 'error');
        }
    }

    /**
     * 删除词表
     */
    async deleteWordlist() {
        const select = document.getElementById('wordlistSelect');
        const name = select.value;
        
        if (!name) {
            this.showMessage('请选择要删除的词表', 'error');
            return;
        }
        
        if (!confirm(`确定要删除词表 "${name}" 吗？此操作不可恢复。`)) {
            return;
        }
        
        try {
            await wordDB.deleteWordlist(name);
            this.showMessage(`词表 "${name}" 已删除`, 'success');
            await this.refreshWordlistSelect();
            this.newWordlist();
        } catch (error) {
            console.error('删除词表失败:', error);
            this.showMessage('删除失败，请重试', 'error');
        }
    }

    /**
     * 刷新词表选择列表
     */
    async refreshWordlistSelect() {
        const select = document.getElementById('wordlistSelect');
        const wordlists = await wordDB.getAllWordlists();
        
        select.innerHTML = '<option value="">选择词表</option>';
        
        wordlists.forEach(wordlist => {
            const option = document.createElement('option');
            option.value = wordlist.name;
            option.textContent = `${wordlist.name} (${wordlist.totalWords}词)`;
            select.appendChild(option);
        });
    }

    /**
     * 导出词表
     */
    async exportWordlists() {
        try {
            const data = await wordDB.exportAllData();
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `wordlists_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.showMessage('词表导出成功', 'success');
        } catch (error) {
            console.error('导出失败:', error);
            this.showMessage('导出失败，请重试', 'error');
        }
    }

    /**
     * 导入词表
     */
    importWordlists() {
        document.getElementById('importFile').click();
    }

    /**
     * 处理文件导入
     */
    async handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        try {
            const text = await this.readFileAsText(file);
            const data = JSON.parse(text);
            
            if (data.wordlists && Array.isArray(data.wordlists)) {
                const success = await wordDB.importData(data);
                if (success) {
                    this.showMessage(`成功导入 ${data.wordlists.length} 个词表`, 'success');
                    await this.refreshWordlistSelect();
                } else {
                    this.showMessage('导入失败，请检查文件格式', 'error');
                }
            } else {
                this.showMessage('文件格式不正确', 'error');
            }
        } catch (error) {
            console.error('导入失败:', error);
            this.showMessage('导入失败，请检查文件格式', 'error');
        }
        
        // 清空文件输入
        event.target.value = '';
    }

    /**
     * 读取文件内容
     */
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    /**
     * 更新单词数量显示
     */
    updateWordCount() {
        const content = document.getElementById('wordlistContent').value.trim();
        if (!content) {
            this.hideWordlistInfo();
            return;
        }
        
        const words = this.parseWordlist(content);
        const name = document.getElementById('wordlistName').value.trim() || '未命名词表';
        this.updateWordlistInfo(name, words);
    }

    /**
     * 更新词表信息显示
     */
    updateWordlistInfo(name, words) {
        document.getElementById('currentWordlistName').textContent = name;
        document.getElementById('wordCount').textContent = words.length;
        document.getElementById('wordlistInfo').classList.remove('hidden');
    }

    /**
     * 隐藏词表信息
     */
    hideWordlistInfo() {
        document.getElementById('wordlistInfo').classList.add('hidden');
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white font-medium z-50 transform transition-all duration-300 translate-x-full`;
        
        // 设置样式
        switch (type) {
            case 'success':
                messageEl.classList.add('bg-green-500');
                break;
            case 'error':
                messageEl.classList.add('bg-red-500');
                break;
            default:
                messageEl.classList.add('bg-blue-500');
        }
        
        messageEl.textContent = message;
        document.body.appendChild(messageEl);
        
        // 显示动画
        setTimeout(() => {
            messageEl.classList.remove('translate-x-full');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            messageEl.classList.add('translate-x-full');
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 获取当前词表的单词
     */
    getCurrentWords() {
        const content = document.getElementById('wordlistContent').value.trim();
        if (!content) return [];
        return this.parseWordlist(content);
    }

    /**
     * 检查是否有可用的词表
     */
    hasWords() {
        return this.getCurrentWords().length > 0;
    }
}

// 创建全局词表管理器实例
window.wordlistManager = new WordlistManager();
