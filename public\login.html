<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能背单词 - 登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .input-focus:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
    </div>

    <!-- 主容器 -->
    <div class="relative z-10 w-full max-w-md px-6">
        <!-- Logo和标题 -->
        <div class="text-center mb-8 fade-in">
            <div class="gradient-bg w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-book-open text-3xl text-white"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">智能背单词</h1>
            <p class="text-gray-600">多用户版 - 支持公共词库和私有词库</p>
        </div>

        <!-- 登录表单 -->
        <div id="loginForm" class="bg-white rounded-2xl card-shadow p-8 fade-in">
            <div class="mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 text-center mb-2">欢迎回来</h2>
                <p class="text-gray-600 text-center">请登录您的账号</p>
            </div>

            <form id="loginFormElement">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2" for="loginUsername">
                        <i class="fas fa-user mr-2"></i>用户名
                    </label>
                    <input 
                        type="text" 
                        id="loginUsername" 
                        name="username"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg input-focus transition-all duration-200"
                        placeholder="请输入用户名"
                        required
                    >
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2" for="loginPassword">
                        <i class="fas fa-lock mr-2"></i>密码
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="loginPassword" 
                            name="password"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg input-focus transition-all duration-200 pr-12"
                            placeholder="请输入密码"
                            required
                        >
                        <button 
                            type="button" 
                            id="toggleLoginPassword"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <button 
                    type="submit" 
                    id="loginBtn"
                    class="w-full btn-primary text-white py-3 rounded-lg font-medium transition-all duration-200 hover:shadow-lg"
                >
                    <span id="loginBtnText">登录</span>
                    <span id="loginBtnLoading" class="loading hidden"></span>
                </button>
            </form>

            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    还没有账号？
                    <button id="showRegisterBtn" class="text-blue-600 hover:text-blue-800 font-medium">
                        立即注册
                    </button>
                </p>
            </div>

            <!-- 默认账号提示 -->
            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-sm text-blue-800">
                    <i class="fas fa-info-circle mr-2"></i>
                    默认管理员账号：<strong>admin</strong> / <strong>admin123</strong>
                </p>
            </div>
        </div>

        <!-- 注册表单 -->
        <div id="registerForm" class="bg-white rounded-2xl card-shadow p-8 fade-in hidden">
            <div class="mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 text-center mb-2">创建账号</h2>
                <p class="text-gray-600 text-center">加入智能背单词社区</p>
            </div>

            <form id="registerFormElement">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2" for="registerUsername">
                        <i class="fas fa-user mr-2"></i>用户名
                    </label>
                    <input 
                        type="text" 
                        id="registerUsername" 
                        name="username"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg input-focus transition-all duration-200"
                        placeholder="3-20个字符，支持中英文"
                        required
                        minlength="3"
                        maxlength="20"
                    >
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2" for="registerEmail">
                        <i class="fas fa-envelope mr-2"></i>邮箱
                    </label>
                    <input 
                        type="email" 
                        id="registerEmail" 
                        name="email"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg input-focus transition-all duration-200"
                        placeholder="请输入邮箱地址"
                        required
                    >
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2" for="registerPassword">
                        <i class="fas fa-lock mr-2"></i>密码
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="registerPassword" 
                            name="password"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg input-focus transition-all duration-200 pr-12"
                            placeholder="至少6个字符"
                            required
                            minlength="6"
                        >
                        <button 
                            type="button" 
                            id="toggleRegisterPassword"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2" for="confirmPassword">
                        <i class="fas fa-lock mr-2"></i>确认密码
                    </label>
                    <input 
                        type="password" 
                        id="confirmPassword" 
                        name="confirmPassword"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg input-focus transition-all duration-200"
                        placeholder="请再次输入密码"
                        required
                    >
                </div>

                <button 
                    type="submit" 
                    id="registerBtn"
                    class="w-full btn-primary text-white py-3 rounded-lg font-medium transition-all duration-200 hover:shadow-lg"
                >
                    <span id="registerBtnText">注册</span>
                    <span id="registerBtnLoading" class="loading hidden"></span>
                </button>
            </form>

            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    已有账号？
                    <button id="showLoginBtn" class="text-blue-600 hover:text-blue-800 font-medium">
                        立即登录
                    </button>
                </p>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

    <script src="/js/auth.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('登录页面DOM加载完成');

            // 确保认证管理器存在
            if (window.authManager) {
                console.log('初始化认证管理器');
                window.authManager.init();
            } else {
                console.error('认证管理器未找到');
            }
        });
    </script>
</body>
</html>
