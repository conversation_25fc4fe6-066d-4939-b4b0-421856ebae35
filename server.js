/**
 * 多用户背单词应用服务器
 * 支持用户认证、公共词库管理、私有词库等功能
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// 导入路由
const { router: authRoutes } = require('./routes/auth');
const wordlistRoutes = require('./routes/wordlists');
const quizRoutes = require('./routes/quiz');
const adminRoutes = require('./routes/admin');

// 导入数据库
const { initDatabase } = require('./models/database');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件 - 暂时禁用CSP以便测试
app.use(helmet({
    contentSecurityPolicy: false
}));

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: '请求过于频繁，请稍后再试'
});
app.use('/api/', limiter);

// 更严格的认证相关API限制
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5, // 限制每个IP 15分钟内最多5次登录尝试
    message: '登录尝试过于频繁，请15分钟后再试'
});
app.use('/api/auth/login', authLimiter);
app.use('/api/auth/register', authLimiter);

// 基础中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));
app.use('/css', express.static(path.join(__dirname, 'css')));
app.use('/js', express.static(path.join(__dirname, 'js')));

// 为根目录也提供静态文件服务（向后兼容）
app.use(express.static(__dirname));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/wordlists', wordlistRoutes);
app.use('/api/quiz', quizRoutes);
app.use('/api/admin', adminRoutes);

// 主页路由 - 重定向到登录页面
app.get('/', (req, res) => {
    res.redirect('/login.html');
});

// 登录页面路由
app.get('/login.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

// 应用主界面（需要登录后访问）
app.get('/app', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: '页面不存在' });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({ 
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? err.message : '请稍后重试'
    });
});

// 初始化数据库并启动服务器
async function startServer() {
    try {
        await initDatabase();
        console.log('数据库初始化完成');
        
        app.listen(PORT, () => {
            console.log(`服务器运行在 http://localhost:${PORT}`);
            console.log('多用户背单词应用已启动');
            console.log('- 访问 http://localhost:3000 进入登录页面');
            console.log('- 默认管理员账号: admin / admin123');
        });
    } catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
}

startServer();

module.exports = app;
