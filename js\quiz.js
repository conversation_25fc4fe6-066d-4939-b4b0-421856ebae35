/**
 * 学习模式模块
 * 处理拼写模式、选择题模式和错题复习功能
 */

class QuizManager {
    constructor() {
        this.currentMode = null;
        this.words = [];
        this.currentWordIndex = 0;
        this.currentWord = null;
        this.correctCount = 0;
        this.wrongCount = 0;
        this.wrongWords = [];
        this.startTime = null;
        this.usedIndexes = [];
        this.countdownTimer = null;
        this.speechSynthesis = window.speechSynthesis;
        this.currentUtterance = null;
        this.hasUserInteracted = false;

        this.initEventListeners();
        this.initSpeechSettings();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 模式选择按钮
        document.getElementById('spellingModeBtn').addEventListener('click', () => this.startSpellingMode());
        document.getElementById('typingModeBtn').addEventListener('click', () => this.startTypingMode());
        document.getElementById('choiceModeBtn').addEventListener('click', () => this.startChoiceMode());
        document.getElementById('reviewModeBtn').addEventListener('click', () => this.startReviewMode());
        document.getElementById('spencerModeBtn').addEventListener('click', () => this.startSpencerMode());
        
        // 学习界面按钮
        document.getElementById('submitBtn').addEventListener('click', () => this.submitAnswer());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextQuestion());
        document.getElementById('backToMenuBtn').addEventListener('click', () => this.backToMenu());
        
        // 完成界面按钮
        document.getElementById('restartBtn').addEventListener('click', () => this.restartQuiz());
        document.getElementById('reviewErrorsBtn').addEventListener('click', () => this.showReviewSelection());
        document.getElementById('backHomeBtn').addEventListener('click', () => this.backToMenu());

        // 复习选择界面按钮
        document.getElementById('startSelectedReviewBtn').addEventListener('click', () => this.startSelectedReview());
        document.getElementById('cancelReviewSelectionBtn').addEventListener('click', () => this.backToMenu());
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }

    /**
     * 开始拼写模式
     */
    async startSpellingMode() {
        if (!this.validateWordlist()) return;

        this.currentMode = 'spelling';
        this.initQuiz();
        this.showQuizContainer();
        this.nextQuestion();
    }

    /**
     * 开始打字模式
     */
    async startTypingMode() {
        if (!this.validateWordlist()) return;

        this.currentMode = 'typing';
        this.initQuiz();
        this.showQuizContainer();
        this.nextQuestion();
    }

    /**
     * 开始选择题模式
     */
    async startChoiceMode() {
        if (!this.validateWordlist()) return;

        if (this.words.length < 4) {
            wordlistManager.showMessage('选择题模式至少需要4个单词', 'error');
            return;
        }

        this.currentMode = 'choice';
        this.initQuiz();
        this.showQuizContainer();
        this.nextQuestion();
    }

    /**
     * 显示复习选择界面
     */
    async showReviewSelection() {
        try {
            const wrongWords = await wordDB.getWrongWords();
            if (wrongWords.length === 0) {
                wordlistManager.showMessage('暂无错题需要复习', 'info');
                return;
            }

            // 存储所有错题数据供筛选使用
            this.allWrongWords = wrongWords;

            // 显示复习选择界面
            document.getElementById('modeSelection').classList.add('hidden');
            document.getElementById('wordlistManager').classList.add('hidden');
            document.getElementById('quizContainer').classList.add('hidden');
            document.getElementById('completionScreen').classList.add('hidden');
            document.getElementById('reviewSelectionScreen').classList.remove('hidden');

            // 初始化复习选择界面
            this.initReviewSelection();
        } catch (error) {
            console.error('加载错题失败:', error);
            wordlistManager.showMessage('加载错题失败', 'error');
        }
    }

    /**
     * 初始化复习选择界面
     */
    initReviewSelection() {
        // 添加筛选条件变化监听
        const filterElements = document.querySelectorAll('input[name="masteryLevel"], input[name="wrongCount"], input[name="reviewStatus"], #reviewLimit');
        filterElements.forEach(element => {
            element.addEventListener('change', () => this.updateReviewPreview());
        });

        // 添加预览展开/收起功能
        const toggleBtn = document.getElementById('togglePreviewBtn');
        const previewContent = document.getElementById('wordPreviewContent');

        toggleBtn.addEventListener('click', () => {
            if (previewContent.classList.contains('hidden')) {
                previewContent.classList.remove('hidden');
                toggleBtn.textContent = '收起';
            } else {
                previewContent.classList.add('hidden');
                toggleBtn.textContent = '展开查看';
            }
        });

        // 添加快速选择预设功能
        const presetBtns = document.querySelectorAll('.preset-btn');
        presetBtns.forEach(btn => {
            btn.addEventListener('click', () => this.applyPreset(btn.dataset.preset));
        });

        // 添加全选功能
        const selectAllCheckbox = document.getElementById('selectAllWords');
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        const selectDifficultBtn = document.getElementById('selectDifficultBtn');

        selectAllCheckbox.addEventListener('change', () => this.toggleSelectAll());
        clearSelectionBtn.addEventListener('click', () => this.clearWordSelection());
        selectDifficultBtn.addEventListener('click', () => this.selectDifficultWords());

        // 初始化选中的单词数组
        this.selectedWords = [];

        // 初始预览
        this.updateReviewPreview();
    }

    /**
     * 更新复习预览
     */
    updateReviewPreview() {
        const filteredWords = this.getFilteredWords();
        document.getElementById('previewCount').textContent = filteredWords.length;

        // 清理无效的选择（筛选后不存在的单词）
        this.cleanupInvalidSelections(filteredWords);

        // 更新单词列表预览
        this.updateWordPreviewList(filteredWords);

        // 显示/隐藏预览列表
        const wordPreviewList = document.getElementById('wordPreviewList');
        if (filteredWords.length === 0) {
            wordPreviewList.classList.add('hidden');
            this.selectedWords = []; // 清空选择
        } else {
            wordPreviewList.classList.remove('hidden');
        }

        // 更新选择UI
        this.updateSelectionUI();
    }

    /**
     * 清理无效的选择
     */
    cleanupInvalidSelections(filteredWords) {
        const validWordIds = filteredWords.map(word => word.id || `word_${filteredWords.indexOf(word)}`);
        this.selectedWords = this.selectedWords.filter(wordId => validWordIds.includes(wordId));
    }

    /**
     * 更新单词预览列表
     */
    updateWordPreviewList(words) {
        const previewItems = document.getElementById('wordPreviewItems');
        const toggleBtn = document.getElementById('togglePreviewBtn');
        const previewContent = document.getElementById('wordPreviewContent');

        if (words.length === 0) {
            previewItems.innerHTML = '';
            return;
        }

        // 限制预览显示的单词数量，避免列表过长
        const displayWords = words.slice(0, 20);
        const hasMore = words.length > 20;

        previewItems.innerHTML = displayWords.map((word, index) => {
            const masteryLevel = word.masteryLevel || 0;
            const wrongCount = word.wrongCount || 1;
            const nextReviewDate = new Date(word.nextReviewDate || new Date());
            const isOverdue = nextReviewDate <= new Date();
            const wordId = word.id || `word_${index}`;
            const isSelected = this.selectedWords.includes(wordId);

            return `
                <div class="word-preview-item flex items-center p-3 bg-gray-50 rounded-lg ${isSelected ? 'ring-2 ring-purple-300 bg-purple-50' : ''}">
                    <label class="flex items-center cursor-pointer flex-1">
                        <input type="checkbox"
                               class="word-checkbox mr-3 filter-checkbox"
                               data-word-id="${wordId}"
                               data-word-index="${index}"
                               ${isSelected ? 'checked' : ''}
                               onchange="quizManager.toggleWordSelection('${wordId}', ${index})">
                        <div class="flex items-center justify-between flex-1">
                            <div class="flex items-center space-x-3">
                                <span class="mastery-indicator mastery-${masteryLevel}">级别${masteryLevel}</span>
                                <div>
                                    <span class="font-medium text-gray-800">${word.chinese}</span>
                                    <span class="text-gray-400 mx-2">→</span>
                                    <span class="text-blue-600 font-medium">${word.english}</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="word-status-tag status-error-count">错${wrongCount}次</span>
                                ${isOverdue ?
                                    '<span class="word-status-tag status-overdue">需复习</span>' :
                                    '<span class="word-status-tag status-learning">学习中</span>'
                                }
                            </div>
                        </div>
                    </label>
                </div>
            `;
        }).join('');

        // 如果有更多单词，显示提示
        if (hasMore) {
            previewItems.innerHTML += `
                <div class="text-center text-sm text-gray-500 py-2 border-t border-gray-200">
                    还有 ${words.length - 20} 个单词未显示...
                </div>
            `;
        }

        // 重置展开状态
        previewContent.classList.add('hidden');
        toggleBtn.textContent = '展开查看';

        // 更新选择状态
        this.updateSelectionUI();
    }

    /**
     * 切换单词选择状态
     */
    toggleWordSelection(wordId, wordIndex) {
        const filteredWords = this.getFilteredWords();
        const word = filteredWords[wordIndex];

        if (!word) return;

        const index = this.selectedWords.indexOf(wordId);
        if (index > -1) {
            // 取消选择
            this.selectedWords.splice(index, 1);
        } else {
            // 添加选择
            this.selectedWords.push(wordId);
        }

        this.updateSelectionUI();
        this.updateWordItemStyle(wordId);
    }

    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllWords');
        const filteredWords = this.getFilteredWords();

        if (selectAllCheckbox.checked) {
            // 全选
            this.selectedWords = filteredWords.map(word => word.id || `word_${filteredWords.indexOf(word)}`);
        } else {
            // 取消全选
            this.selectedWords = [];
        }

        this.updateSelectionUI();
        this.updateAllWordItemStyles();
    }

    /**
     * 清空选择
     */
    clearWordSelection() {
        this.selectedWords = [];
        document.getElementById('selectAllWords').checked = false;
        this.updateSelectionUI();
        this.updateAllWordItemStyles();
    }

    /**
     * 选择困难单词（错误次数>=3次或掌握程度<=1级）
     */
    selectDifficultWords() {
        const filteredWords = this.getFilteredWords();
        this.selectedWords = [];

        filteredWords.forEach((word, index) => {
            const wrongCount = word.wrongCount || 1;
            const masteryLevel = word.masteryLevel || 0;

            // 困难单词的条件：错误次数>=3次 或 掌握程度<=1级
            if (wrongCount >= 3 || masteryLevel <= 1) {
                const wordId = word.id || `word_${index}`;
                this.selectedWords.push(wordId);
            }
        });

        this.updateSelectionUI();
        this.updateAllWordItemStyles();

        // 视觉反馈
        if (this.selectedWords.length > 0) {
            wordlistManager.showMessage(`已选择 ${this.selectedWords.length} 个困难单词`, 'success');
        } else {
            wordlistManager.showMessage('当前筛选结果中没有困难单词', 'info');
        }
    }

    /**
     * 更新选择UI状态
     */
    updateSelectionUI() {
        const selectedCount = this.selectedWords.length;
        const filteredWords = this.getFilteredWords();
        const totalCount = filteredWords.length;

        // 更新选择计数
        document.getElementById('selectedCount').textContent = selectedCount;

        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('selectAllWords');
        if (selectedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (selectedCount === totalCount) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }

        // 更新开始按钮状态
        const startBtn = document.getElementById('startSelectedReviewBtn');
        if (selectedCount === 0) {
            startBtn.disabled = true;
            startBtn.classList.add('opacity-50', 'cursor-not-allowed');
            startBtn.innerHTML = '<i class="fas fa-play mr-2"></i>请选择单词';
        } else {
            startBtn.disabled = false;
            startBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            startBtn.innerHTML = `<i class="fas fa-play mr-2"></i>开始复习 (${selectedCount}个)`;
        }
    }

    /**
     * 更新单个单词项的样式
     */
    updateWordItemStyle(wordId) {
        const checkbox = document.querySelector(`input[data-word-id="${wordId}"]`);
        if (!checkbox) return;

        const wordItem = checkbox.closest('.word-preview-item');
        const isSelected = this.selectedWords.includes(wordId);

        checkbox.checked = isSelected;

        if (isSelected) {
            wordItem.classList.add('ring-2', 'ring-purple-300', 'bg-purple-50');
            wordItem.classList.remove('bg-gray-50');
        } else {
            wordItem.classList.remove('ring-2', 'ring-purple-300', 'bg-purple-50');
            wordItem.classList.add('bg-gray-50');
        }
    }

    /**
     * 更新所有单词项的样式
     */
    updateAllWordItemStyles() {
        const checkboxes = document.querySelectorAll('.word-checkbox');
        checkboxes.forEach(checkbox => {
            const wordId = checkbox.dataset.wordId;
            this.updateWordItemStyle(wordId);
        });
    }

    /**
     * 根据筛选条件获取错题
     */
    getFilteredWords() {
        if (!this.allWrongWords) return [];

        const now = new Date();
        let filteredWords = [...this.allWrongWords];

        // 掌握程度筛选
        const selectedMasteryLevels = Array.from(document.querySelectorAll('input[name="masteryLevel"]:checked'))
            .map(input => parseInt(input.value));
        if (selectedMasteryLevels.length > 0) {
            filteredWords = filteredWords.filter(word =>
                selectedMasteryLevels.includes(word.masteryLevel || 0)
            );
        }

        // 错误次数筛选
        const selectedWrongCounts = Array.from(document.querySelectorAll('input[name="wrongCount"]:checked'))
            .map(input => input.value);
        if (selectedWrongCounts.length > 0) {
            filteredWords = filteredWords.filter(word => {
                const wrongCount = word.wrongCount || 1;
                return selectedWrongCounts.some(range => {
                    if (range === '1-2') return wrongCount >= 1 && wrongCount <= 2;
                    if (range === '3-5') return wrongCount >= 3 && wrongCount <= 5;
                    if (range === '6+') return wrongCount >= 6;
                    return false;
                });
            });
        }

        // 复习状态筛选
        const selectedReviewStatus = Array.from(document.querySelectorAll('input[name="reviewStatus"]:checked'))
            .map(input => input.value);
        if (selectedReviewStatus.length > 0 && !selectedReviewStatus.includes('all')) {
            filteredWords = filteredWords.filter(word => {
                const nextReviewDate = new Date(word.nextReviewDate || now);
                const isDue = nextReviewDate <= now;
                const isLearning = !isDue && (word.masteryLevel || 0) < 5;

                return selectedReviewStatus.some(status => {
                    if (status === 'due') return isDue;
                    if (status === 'learning') return isLearning;
                    return false;
                });
            });
        }

        // 数量限制
        const limit = document.getElementById('reviewLimit').value;
        if (limit !== 'all') {
            const limitNum = parseInt(limit);
            // 按优先级排序：掌握程度低的优先，然后按下次复习时间
            filteredWords.sort((a, b) => {
                if ((a.masteryLevel || 0) !== (b.masteryLevel || 0)) {
                    return (a.masteryLevel || 0) - (b.masteryLevel || 0);
                }
                return new Date(a.nextReviewDate || now) - new Date(b.nextReviewDate || now);
            });
            filteredWords = filteredWords.slice(0, limitNum);
        }

        return filteredWords;
    }

    /**
     * 开始选定的复习
     */
    async startSelectedReview() {
        // 检查是否有选中的单词
        if (this.selectedWords.length === 0) {
            wordlistManager.showMessage('请先选择要复习的单词', 'error');
            return;
        }

        const filteredWords = this.getFilteredWords();

        // 只获取选中的单词
        const selectedWordsData = filteredWords.filter(word => {
            const wordId = word.id || `word_${filteredWords.indexOf(word)}`;
            return this.selectedWords.includes(wordId);
        });

        if (selectedWordsData.length === 0) {
            wordlistManager.showMessage('选中的单词无效，请重新选择', 'error');
            return;
        }

        // 获取选择的复习模式
        const reviewMode = document.querySelector('input[name="reviewMode"]:checked').value;

        this.words = selectedWordsData.map(item => ({
            chinese: item.chinese,
            english: item.english,
            id: item.id,
            masteryLevel: item.masteryLevel || 0,
            interval: item.interval || 1,
            wrongCount: item.wrongCount || 1
        }));

        this.currentMode = reviewMode === 'spencer' ? 'spencer' : 'review';
        this.initQuiz();
        this.showQuizContainer();

        if (reviewMode === 'spencer') {
            this.showSpencerInfo();
        }

        // 显示选择信息
        wordlistManager.showMessage(`开始复习 ${selectedWordsData.length} 个选中的单词`, 'success');

        this.nextQuestion();
    }

    /**
     * 应用快速选择预设
     */
    applyPreset(preset) {
        // 清除所有选择
        document.querySelectorAll('input[name="masteryLevel"]').forEach(input => input.checked = false);
        document.querySelectorAll('input[name="wrongCount"]').forEach(input => input.checked = false);
        document.querySelectorAll('input[name="reviewStatus"]').forEach(input => input.checked = false);

        switch (preset) {
            case 'difficult':
                // 困难单词：错误次数多，掌握程度低
                document.querySelector('input[name="masteryLevel"][value="0"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="1"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="3-5"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="6+"]').checked = true;
                document.querySelector('input[name="reviewStatus"][value="all"]').checked = true;
                document.getElementById('reviewLimit').value = '20';
                break;

            case 'due':
                // 需要复习：到期的单词
                document.querySelector('input[name="masteryLevel"][value="0"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="1"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="2"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="3"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="1-2"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="3-5"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="6+"]').checked = true;
                document.querySelector('input[name="reviewStatus"][value="due"]').checked = true;
                document.getElementById('reviewLimit').value = '30';
                break;

            case 'beginner':
                // 初学单词：掌握程度低，错误次数少
                document.querySelector('input[name="masteryLevel"][value="0"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="1"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="2"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="1-2"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="3-5"]').checked = true;
                document.querySelector('input[name="reviewStatus"][value="all"]').checked = true;
                document.getElementById('reviewLimit').value = '20';
                break;

            case 'all':
                // 全部错题
                document.querySelectorAll('input[name="masteryLevel"]').forEach(input => input.checked = true);
                document.querySelectorAll('input[name="wrongCount"]').forEach(input => input.checked = true);
                document.querySelector('input[name="reviewStatus"][value="all"]').checked = true;
                document.getElementById('reviewLimit').value = 'all';
                break;
        }

        // 更新预览
        this.updateReviewPreview();

        // 视觉反馈
        const clickedBtn = document.querySelector(`[data-preset="${preset}"]`);
        clickedBtn.classList.add('ring-2', 'ring-offset-2');
        setTimeout(() => {
            clickedBtn.classList.remove('ring-2', 'ring-offset-2');
        }, 1000);
    }

    /**
     * 开始错题复习模式（保留原有方法用于兼容）
     */
    async startReviewMode() {
        this.showReviewSelection();
    }

    /**
     * 开始斯宾塞复习模式（间隔重复）
     */
    async startSpencerMode() {
        try {
            const wordsForReview = await wordDB.getWordsForReview();
            if (wordsForReview.length === 0) {
                // 显示复习统计信息
                const stats = await wordDB.getWrongWordsStats();
                this.showSpencerStats(stats);
                return;
            }

            this.words = wordsForReview.map(item => ({
                chinese: item.chinese,
                english: item.english,
                id: item.id,
                masteryLevel: item.masteryLevel,
                interval: item.interval,
                wrongCount: item.wrongCount
            }));

            this.currentMode = 'spencer';
            this.initQuiz();
            this.showQuizContainer();
            this.showSpencerInfo();
            this.nextQuestion();
        } catch (error) {
            console.error('加载斯宾塞复习失败:', error);
            wordlistManager.showMessage('加载斯宾塞复习失败', 'error');
        }
    }

    /**
     * 初始化语音设置
     */
    initSpeechSettings() {
        // 检查浏览器是否支持语音合成
        this.speechSupported = 'speechSynthesis' in window;

        if (this.speechSupported) {
            // 从本地存储加载设置，或使用默认设置
            const savedSettings = localStorage.getItem('speechSettings');
            this.speechSettings = savedSettings ?
                JSON.parse(savedSettings) :
                {
                    enabled: true, // 默认启用语音
                    rate: 0.8,     // 语速
                    pitch: 1,      // 音调
                    volume: 0.8,   // 音量
                    lang: 'en-US'  // 语言
                };

            // 等待语音列表加载
            this.loadVoices();
        } else {
            console.warn('浏览器不支持语音合成功能');
            this.speechSettings = { enabled: false };
        }
    }

    /**
     * 加载可用的语音
     */
    loadVoices() {
        const voices = this.speechSynthesis.getVoices();

        if (voices.length === 0) {
            // 某些浏览器需要异步加载语音列表
            this.speechSynthesis.onvoiceschanged = () => {
                this.availableVoices = this.speechSynthesis.getVoices();
                this.selectBestVoice();
            };
        } else {
            this.availableVoices = voices;
            this.selectBestVoice();
        }
    }

    /**
     * 选择最佳语音
     */
    selectBestVoice() {
        if (!this.availableVoices || this.availableVoices.length === 0) return;

        // 优先选择英语语音
        const englishVoices = this.availableVoices.filter(voice =>
            voice.lang.startsWith('en')
        );

        if (englishVoices.length > 0) {
            // 优先选择美式英语，然后是英式英语
            this.selectedVoice = englishVoices.find(voice => voice.lang === 'en-US') ||
                                englishVoices.find(voice => voice.lang === 'en-GB') ||
                                englishVoices[0];
        } else {
            // 如果没有英语语音，使用默认语音
            this.selectedVoice = this.availableVoices[0];
        }
    }

    /**
     * 验证词表
     */
    validateWordlist() {
        if (!wordlistManager.hasWords()) {
            wordlistManager.showMessage('请先输入单词内容', 'error');
            return false;
        }
        return true;
    }

    /**
     * 初始化测验
     */
    initQuiz() {
        if (this.currentMode !== 'review') {
            this.words = wordlistManager.getCurrentWords();
        }

        this.currentWordIndex = 0;
        this.correctCount = 0;
        this.wrongCount = 0;
        this.wrongWords = [];
        this.startTime = Date.now();
        this.usedIndexes = [];
        this.hasUserInteracted = false; // 重置用户交互状态

        // 打乱单词顺序
        this.shuffleArray(this.words);

        this.updateStats();
        this.updateProgress();
    }

    /**
     * 显示测验容器
     */
    showQuizContainer() {
        document.getElementById('modeSelection').classList.add('hidden');
        document.getElementById('wordlistManager').classList.add('hidden');
        document.getElementById('quizContainer').classList.remove('hidden');
        document.getElementById('completionScreen').classList.add('hidden');
    }

    /**
     * 下一题
     */
    nextQuestion() {
        // 清除可能存在的倒计时
        this.clearAutoNextCountdown();

        if (this.usedIndexes.length >= this.words.length) {
            this.completeQuiz();
            return;
        }

        // 随机选择未使用的单词
        let wordIndex;
        do {
            wordIndex = Math.floor(Math.random() * this.words.length);
        } while (this.usedIndexes.includes(wordIndex));

        this.usedIndexes.push(wordIndex);
        this.currentWord = this.words[wordIndex];
        this.currentWordIndex = this.usedIndexes.length;

        this.updateProgress();
        this.clearResult();

        if (this.currentMode === 'spelling' || this.currentMode === 'review') {
            this.showSpellingQuestion();
        } else if (this.currentMode === 'typing') {
            this.showTypingQuestion();
        } else if (this.currentMode === 'choice') {
            this.showChoiceQuestion();
        }
    }

    /**
     * 显示拼写题目
     */
    showSpellingQuestion() {
        document.getElementById('questionText').innerHTML = `
            <div class="flex items-center justify-center space-x-4">
                <span>请输入"${this.currentWord.chinese}"的英文：</span>
                <button id="playAudioBtn" class="audio-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors" title="播放发音">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
        `;

        const answerArea = document.getElementById('answerArea');
        answerArea.innerHTML = `
            <input type="text" id="spellingInput"
                   class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none text-center"
                   placeholder="输入英文单词" autocomplete="off">
        `;

        document.getElementById('submitBtn').classList.remove('hidden');
        document.getElementById('nextBtn').classList.add('hidden');

        // 添加语音播放按钮事件
        document.getElementById('playAudioBtn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleAudioButtonClick();
        });

        // 聚焦输入框
        setTimeout(() => {
            document.getElementById('spellingInput').focus();
        }, 100);

        // 延迟自动播放，确保用户已经交互过
        if (this.speechSettings.enabled) {
            this.scheduleAutoPlay();
        }
    }

    /**
     * 显示选择题目
     */
    showChoiceQuestion() {
        document.getElementById('questionText').innerHTML = `
            <div class="flex items-center justify-center space-x-4">
                <span>"${this.currentWord.chinese}"的英文是：</span>
                <button id="playAudioBtn" class="audio-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors" title="播放发音">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
        `;

        // 生成选项
        const options = [this.currentWord.english];
        while (options.length < 4) {
            const randomWord = this.words[Math.floor(Math.random() * this.words.length)];
            if (!options.includes(randomWord.english)) {
                options.push(randomWord.english);
            }
        }

        this.shuffleArray(options);

        const answerArea = document.getElementById('answerArea');
        answerArea.innerHTML = options.map((option, index) => `
            <button class="choice-option w-full p-4 mb-3 text-left border-2 border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
                    data-answer="${option}">
                ${String.fromCharCode(65 + index)}. ${option}
            </button>
        `).join('');

        // 添加选项点击事件
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.addEventListener('click', (e) => this.selectChoice(e.target));
        });

        // 添加语音播放按钮事件
        document.getElementById('playAudioBtn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleAudioButtonClick();
        });

        document.getElementById('submitBtn').classList.add('hidden');
        document.getElementById('nextBtn').classList.add('hidden');

        // 延迟自动播放，确保用户已经交互过
        if (this.speechSettings.enabled) {
            this.scheduleAutoPlay();
        }
    }

    /**
     * 显示打字题目
     */
    showTypingQuestion() {
        document.getElementById('questionText').innerHTML = `
            <div class="flex items-center justify-center space-x-4">
                <span>请逐字母输入"${this.currentWord.chinese}"的英文：</span>
                <button id="playAudioBtn" class="audio-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors" title="播放发音">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
        `;

        const answerArea = document.getElementById('answerArea');
        const englishWord = this.currentWord.english.toLowerCase();

        // 创建字母输入框
        let inputBoxes = '';
        for (let i = 0; i < englishWord.length; i++) {
            const letter = englishWord[i];
            if (letter === ' ') {
                // 空格显示为间隔
                inputBoxes += '<div class="w-4"></div>';
            } else {
                inputBoxes += `
                    <div class="flex flex-col items-center">
                        <input type="text"
                               class="typing-input w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none uppercase"
                               maxlength="1"
                               data-index="${i}"
                               data-letter="${letter}"
                               autocomplete="off">
                        <div class="correct-letter-display text-success w-12 h-8 flex items-center justify-center text-xl font-bold mt-1 opacity-0 transition-opacity duration-300"></div>
                    </div>
                `;
            }
        }

        answerArea.innerHTML = `
            <div class="flex flex-wrap justify-center items-start gap-2 mb-4" id="typingInputContainer">
                ${inputBoxes}
            </div>
            <div class="text-sm text-gray-500 mb-4">
                <i class="fas fa-info-circle mr-1"></i>
                提示：${englishWord.includes(' ') ?
                    `词组包含 ${englishWord.replace(/\s/g, '').length} 个字母，空格已预留位置` :
                    `单词长度为 ${englishWord.length} 个字母`
                }
            </div>
            <button id="submitTypingBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                提交答案
            </button>
        `;

        // 添加输入框事件监听器
        this.initTypingInputs();

        // 添加音频播放按钮事件
        document.getElementById('playAudioBtn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleAudioButtonClick();
        });

        // 添加提交按钮事件
        document.getElementById('submitTypingBtn').addEventListener('click', () => {
            this.submitTypingAnswer();
        });

        // 隐藏默认的提交和下一题按钮
        document.getElementById('submitBtn').classList.add('hidden');
        document.getElementById('nextBtn').classList.add('hidden');

        // 延迟自动播放，确保用户已经交互过
        if (this.speechSettings.enabled) {
            this.scheduleAutoPlay();
        }

        // 设置第一个输入框焦点
        setTimeout(() => {
            const firstInput = document.querySelector('.typing-input');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }

    /**
     * 选择选项
     */
    selectChoice(button) {
        // 防止重复点击
        if (button.disabled) return;

        // 禁用所有选项
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.disabled = true;
        });

        // 移除其他选项的选中状态
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.classList.remove('bg-blue-500', 'text-white', 'border-blue-500');
            btn.classList.add('border-gray-300');
        });

        // 标记当前选项为选中
        button.classList.add('bg-blue-500', 'text-white', 'border-blue-500');
        button.classList.remove('border-gray-300');

        // 立即提交答案（不需要额外延迟，因为submitAnswer中已有0.5s延迟）
        this.submitAnswer(button.dataset.answer);
    }

    /**
     * 初始化打字输入框
     */
    initTypingInputs() {
        const inputs = document.querySelectorAll('.typing-input');

        inputs.forEach((input, index) => {
            // 输入事件
            input.addEventListener('input', (e) => {
                const value = e.target.value.toLowerCase();

                // 只允许输入字母
                if (value && !/^[a-z]$/.test(value)) {
                    e.target.value = '';
                    return;
                }

                // 如果输入了字母，自动跳到下一个输入框
                if (value) {
                    const nextInput = inputs[index + 1];
                    if (nextInput) {
                        nextInput.focus();
                    }
                }

                // 检查是否所有输入框都已填写
                this.checkTypingCompletion();
            });

            // 键盘事件
            input.addEventListener('keydown', (e) => {
                // 退格键跳到上一个输入框
                if (e.key === 'Backspace' && !e.target.value) {
                    const prevInput = inputs[index - 1];
                    if (prevInput) {
                        prevInput.focus();
                        prevInput.value = '';
                    }
                }

                // 回车键提交答案
                if (e.key === 'Enter') {
                    this.submitTypingAnswer();
                }
            });

            // 粘贴事件
            input.addEventListener('paste', (e) => {
                e.preventDefault();
                const clipboardData = e.clipboardData || window['clipboardData'];
                const pastedText = clipboardData.getData('text').toLowerCase();

                // 清空所有输入框
                inputs.forEach(inp => inp.value = '');

                // 智能分配粘贴的文本到输入框，跳过空格
                const correctAnswer = this.currentWord.english.toLowerCase();
                let pasteIndex = 0;
                let inputIndex = 0;

                for (let i = 0; i < correctAnswer.length && pasteIndex < pastedText.length; i++) {
                    if (correctAnswer[i] === ' ') {
                        // 跳过空格
                        continue;
                    } else {
                        // 查找下一个非空格字符
                        while (pasteIndex < pastedText.length && pastedText[pasteIndex] === ' ') {
                            pasteIndex++;
                        }

                        if (pasteIndex < pastedText.length && inputIndex < inputs.length) {
                            const char = pastedText[pasteIndex];
                            if (/^[a-z]$/.test(char)) {
                                inputs[inputIndex].value = char;
                            }
                            pasteIndex++;
                            inputIndex++;
                        }
                    }
                }

                // 检查完成状态
                this.checkTypingCompletion();

                // 焦点移到第一个空的输入框
                const firstEmptyInput = Array.from(inputs).find(inp => !inp.value);
                if (firstEmptyInput) {
                    firstEmptyInput.focus();
                } else if (inputs.length > 0) {
                    inputs[inputs.length - 1].focus();
                }
            });
        });
    }

    /**
     * 检查打字完成状态
     */
    checkTypingCompletion() {
        const inputs = document.querySelectorAll('.typing-input');
        const submitBtn = document.getElementById('submitTypingBtn');

        const allFilled = Array.from(inputs).every(input => input.value.trim() !== '');

        if (submitBtn) {
            submitBtn.disabled = !allFilled;
        }
    }

    /**
     * 提交打字答案
     */
    async submitTypingAnswer() {
        const inputs = document.querySelectorAll('.typing-input');
        const correctAnswer = this.currentWord.english.toLowerCase();

        // 重建用户答案，在正确位置插入空格
        let userAnswer = '';
        let inputIndex = 0;

        for (let i = 0; i < correctAnswer.length; i++) {
            if (correctAnswer[i] === ' ') {
                userAnswer += ' ';
            } else {
                if (inputIndex < inputs.length) {
                    userAnswer += inputs[inputIndex].value.toLowerCase();
                    inputIndex++;
                } else {
                    // 如果输入框不够，说明有问题
                    userAnswer += '';
                }
            }
        }

        if (!userAnswer.trim()) {
            wordlistManager.showMessage('请完成所有字母输入', 'error');
            return;
        }

        const isCorrect = userAnswer === correctAnswer;

        // 禁用所有输入框并进行逐字母对比
        this.compareLettersAndHighlight(inputs, correctAnswer, isCorrect);

        // 禁用提交按钮
        document.getElementById('submitTypingBtn').disabled = true;

        if (isCorrect) {
            this.correctCount++;
            this.showTypingResult('正确！', 'success', this.currentWord);

            // 如果是斯宾塞模式，更新复习记录
            if (this.currentMode === 'spencer' && this.currentWord.id) {
                try {
                    await wordDB.updateWrongWordReview(this.currentWord.id, true, 4);
                } catch (error) {
                    console.error('更新斯宾塞复习记录失败:', error);
                }
            }
        } else {
            this.wrongCount++;
            this.wrongWords.push(this.currentWord);

            if (this.currentMode === 'spencer' && this.currentWord.id) {
                // 斯宾塞模式：更新复习记录
                try {
                    await wordDB.updateWrongWordReview(this.currentWord.id, false, 1);
                } catch (error) {
                    console.error('更新斯宾塞复习记录失败:', error);
                }
            } else {
                // 普通模式：添加到错题本
                try {
                    await wordDB.addWrongWord(1, this.currentWord.chinese, this.currentWord.english);
                } catch (error) {
                    console.error('添加错题失败:', error);
                }
            }

            this.showTypingResult('错误！', 'error', this.currentWord);
        }

        this.updateStats();

        // 显示倒计时进度条，答对1.5秒，答错3秒
        this.showAutoNextCountdown(isCorrect);
    }

    /**
     * 逐字母对比并高亮显示
     */
    compareLettersAndHighlight(inputs, correctAnswer, isCorrect) {
        let inputIndex = 0;

        // 遍历正确答案的每个字符，设置输入框样式和显示正确字母
        for (let i = 0; i < correctAnswer.length; i++) {
            const correctChar = correctAnswer[i];

            // 跳过空格
            if (correctChar === ' ') {
                continue;
            }

            // 确保还有输入框可以处理
            if (inputIndex >= inputs.length) {
                break;
            }

            const input = inputs[inputIndex];
            const userChar = input.value.toLowerCase();
            const letterDisplay = input.parentElement.querySelector('.correct-letter-display');

            // 禁用输入框
            input.disabled = true;

            // 根据字母是否正确设置样式
            if (userChar === correctChar) {
                // 正确的字母 - 保持原样，不加背景色
                input.classList.remove('bg-red-100', 'border-red-500', 'text-red-700');

                // 显示正确字母（灰色）
                if (letterDisplay && !isCorrect) {
                    letterDisplay.textContent = correctChar.toUpperCase();
                    letterDisplay.classList.remove('opacity-0', 'text-black');
                    letterDisplay.classList.add('opacity-100', 'text-gray-400');
                }
            } else {
                // 错误的字母 - 红色
                input.classList.add('bg-red-100', 'border-red-500', 'text-red-700');

                // 显示正确字母（黑色）
                if (letterDisplay) {
                    letterDisplay.textContent = correctChar.toUpperCase();
                    letterDisplay.classList.remove('opacity-0', 'text-gray-400');
                    letterDisplay.classList.add('opacity-100', 'text-green-600');

                    // 2秒后停止脉冲动画
                    setTimeout(() => {
                        letterDisplay.classList.remove('animate-pulse');
                    }, 1500);
                }

                // 如果输入框为空，显示占位符
                if (!userChar) {
                    input.placeholder = '?';
                    input.classList.add('placeholder-gray-400');
                }
            }

            inputIndex++;
        }

        // 处理多余的输入框（如果用户输入的字母比正确答案多）
        while (inputIndex < inputs.length) {
            const input = inputs[inputIndex];
            const letterDisplay = input.parentElement.querySelector('.correct-letter-display');

            input.disabled = true;

            if (input.value.trim()) {
                // 多余的输入 - 红色
                input.classList.add('bg-red-100', 'border-red-500', 'text-red-700');

                // 显示"多余"标记
                if (letterDisplay) {
                    letterDisplay.textContent = '×';
                    letterDisplay.classList.remove('opacity-0', 'text-gray-400');
                    letterDisplay.classList.add('opacity-100', 'text-red-600');
                }
            }

            inputIndex++;
        }

        // 如果整体答案错误，添加错误动画
        if (!isCorrect) {
            inputs.forEach((input, index) => {
                setTimeout(() => {
                    input.classList.add('animate-shake');
                    setTimeout(() => {
                        input.classList.remove('animate-shake');
                    }, 600);
                }, index * 50); // 错开动画时间，创造波浪效果
            });
        }
    }

    /**
     * 显示打字模式结果
     */
    showTypingResult(message, type, word) {
        const resultArea = document.getElementById('resultArea');
        const isCorrect = type === 'success';

        resultArea.innerHTML = `
            <div class="p-6 rounded-xl ${isCorrect ? 'bg-green-50 border-2 border-green-200 result-success' : 'bg-red-50 border-2 border-red-200 result-error'} text-center">
                <!-- 结果图标和状态 -->
                <div class="mb-6">
                    <i class="fas ${isCorrect ? 'fa-check-circle' : 'fa-times-circle'} text-7xl ${isCorrect ? 'text-green-500' : 'text-red-500'} mb-4 drop-shadow-lg"></i>
                    <div class="text-3xl font-bold ${isCorrect ? 'text-green-800' : 'text-red-800'} mb-2">
                        ${message}
                    </div>
                    ${isCorrect ?
                        '<div class="text-green-600 text-lg">太棒了！每个字母都正确！</div>' :
                        '<div class="text-red-600 text-lg">查看输入框下方的正确字母</div>'
                    }
                </div>

                <!-- 单词信息 -->
                <div class="word-display bg-white rounded-xl p-6 mb-6 shadow-lg">
                    <div class="text-xl font-medium text-gray-700 mb-3">
                        <i class="fas fa-language mr-2 text-blue-500"></i>
                        <span class="text-blue-600 text-2xl font-semibold">${word.chinese}</span>
                        ${this.currentMode === 'spencer' && word.masteryLevel !== undefined ? `
                            <span class="mastery-indicator mastery-${word.masteryLevel} ml-3">
                                级别 ${word.masteryLevel}
                            </span>
                        ` : ''}
                    </div>
                    <div class="text-3xl font-bold ${isCorrect ? 'text-green-600' : 'text-red-600'} mb-2">
                        <i class="fas fa-globe mr-2"></i>
                        ${word.english}
                    </div>

                    ${this.currentMode === 'spencer' && word.interval !== undefined ? `
                        <div class="text-sm text-gray-500 mb-2">
                            <i class="fas fa-clock mr-1"></i>
                            复习间隔: ${word.interval} 天 | 错误次数: ${word.wrongCount || 0}
                        </div>
                    ` : ''}
                    ${!isCorrect ?
                        '<div class="text-red-500 font-medium mt-3 p-2 bg-red-50 rounded-lg">💡 黑色字母是您需要注意的部分，灰色字母表示您答对了</div>' :
                        this.currentMode === 'spencer' ?
                            '<div class="text-green-500 font-medium mt-3">🧠 记忆强化成功！下次复习间隔将延长</div>' :
                            '<div class="text-green-500 font-medium mt-3">✨ 每个字母都完美！</div>'
                    }
                </div>

                <!-- 弱化的倒计时显示 -->
                <div class="mt-4 opacity-50">
                    <div class="flex items-center justify-center text-sm text-gray-400 mb-2">
                        <i class="fas fa-clock mr-1"></i>
                        <span id="countdownText">${isCorrect ? '1.5' : '3.0'}s</span>
                        <span class="ml-1">后自动切换</span>
                    </div>
                    <div class="w-24 bg-gray-200 rounded-full h-1 cursor-pointer mx-auto" onclick="quizManager.nextQuestion()" title="点击立即切换">
                        <div id="countdownBar" class="bg-gray-400 h-1 rounded-full transition-all duration-100" style="width: 100%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">点击可跳过</div>
                </div>
            </div>
        `;
    }



    /**
     * 提交答案
     */
    async submitAnswer(choiceAnswer = null) {
        let userAnswer;

        if (this.currentMode === 'choice') {
            userAnswer = choiceAnswer;
        } else if (this.currentMode === 'typing') {
            // 打字模式使用专门的提交方法
            return this.submitTypingAnswer();
        } else {
            const input = document.getElementById('spellingInput');
            if (!input) return;
            userAnswer = input.value.trim().toLowerCase();
        }

        if (!userAnswer) {
            wordlistManager.showMessage('请输入答案', 'error');
            return;
        }

        const correctAnswer = this.currentWord.english.toLowerCase();
        const isCorrect = userAnswer === correctAnswer;

        if (isCorrect) {
            this.correctCount++;
            this.showResult('正确！', 'success', this.currentWord);

            // 如果是斯宾塞模式，更新复习记录
            if (this.currentMode === 'spencer' && this.currentWord.id) {
                try {
                    await wordDB.updateWrongWordReview(this.currentWord.id, true, 4);
                } catch (error) {
                    console.error('更新斯宾塞复习记录失败:', error);
                }
            }
        } else {
            this.wrongCount++;
            this.wrongWords.push(this.currentWord);

            if (this.currentMode === 'spencer' && this.currentWord.id) {
                // 斯宾塞模式：更新复习记录
                try {
                    await wordDB.updateWrongWordReview(this.currentWord.id, false, 1);
                } catch (error) {
                    console.error('更新斯宾塞复习记录失败:', error);
                }
            } else {
                // 普通模式：添加到错题本
                try {
                    await wordDB.addWrongWord(1, this.currentWord.chinese, this.currentWord.english);
                } catch (error) {
                    console.error('添加错题失败:', error);
                }
            }

            this.showResult('错误！', 'error', this.currentWord);
        }

        this.updateStats();

        // 禁用输入
        if (this.currentMode !== 'choice') {
            document.getElementById('spellingInput').disabled = true;
        }

        document.getElementById('submitBtn').classList.add('hidden');
        document.getElementById('nextBtn').classList.remove('hidden');

        // 显示倒计时进度条，答对1.5秒，答错3秒
        this.showAutoNextCountdown(isCorrect);
    }

    /**
     * 显示结果
     */
    showResult(message, type, word) {
        const resultArea = document.getElementById('resultArea');
        const isCorrect = type === 'success';

        resultArea.innerHTML = `
            <div class="p-6 rounded-xl ${isCorrect ? 'bg-green-50 border-2 border-green-200 result-success' : 'bg-red-50 border-2 border-red-200 result-error'} text-center">
                <!-- 结果图标和状态 -->
                <div class="mb-6">
                    <i class="fas ${isCorrect ? 'fa-check-circle' : 'fa-times-circle'} text-7xl ${isCorrect ? 'text-green-500' : 'text-red-500'} mb-4 drop-shadow-lg"></i>
                    <div class="text-3xl font-bold ${isCorrect ? 'text-green-800' : 'text-red-800'} mb-2">
                        ${message}
                    </div>
                    ${isCorrect ?
                        '<div class="text-green-600 text-lg">太棒了！继续加油！</div>' :
                        '<div class="text-red-600 text-lg">没关系，继续努力！</div>'
                    }
                </div>

                <!-- 单词信息 -->
                <div class="word-display bg-white rounded-xl p-6 mb-6 shadow-lg">
                    <div class="text-xl font-medium text-gray-700 mb-3">
                        <i class="fas fa-language mr-2 text-blue-500"></i>
                        <span class="text-blue-600 text-2xl font-semibold">${word.chinese}</span>
                        ${this.currentMode === 'spencer' && word.masteryLevel !== undefined ? `
                            <span class="mastery-indicator mastery-${word.masteryLevel} ml-3">
                                级别 ${word.masteryLevel}
                            </span>
                        ` : ''}
                    </div>
                    <div class="text-3xl font-bold ${isCorrect ? 'text-green-600' : 'text-red-600'} mb-2">
                        <i class="fas fa-globe mr-2"></i>
                        ${word.english}
                    </div>
                    ${this.currentMode === 'spencer' && word.interval !== undefined ? `
                        <div class="text-sm text-gray-500 mb-2">
                            <i class="fas fa-clock mr-1"></i>
                            复习间隔: ${word.interval} 天 | 错误次数: ${word.wrongCount || 0}
                        </div>
                    ` : ''}
                    ${!isCorrect ?
                        '<div class="text-red-500 font-medium mt-3 p-2 bg-red-50 rounded-lg">💡 请仔细记住这个单词的拼写</div>' :
                        this.currentMode === 'spencer' ?
                            '<div class="text-green-500 font-medium mt-3">🧠 记忆强化成功！下次复习间隔将延长</div>' :
                            '<div class="text-green-500 font-medium mt-3">✨ 记忆得很好！</div>'
                    }
                </div>

                <!-- 弱化的倒计时显示 -->
                <div class="mt-4 opacity-50">
                    <div class="flex items-center justify-center text-sm text-gray-400 mb-2">
                        <i class="fas fa-clock mr-1"></i>
                        <span id="countdownText">${isCorrect ? '1.5' : '3.0'}s</span>
                        <span class="ml-1">后自动切换</span>
                    </div>
                    <div class="w-24 bg-gray-200 rounded-full h-1 cursor-pointer mx-auto" onclick="quizManager.nextQuestion()" title="点击立即切换">
                        <div id="countdownBar" class="bg-gray-400 h-1 rounded-full transition-all duration-100" style="width: 100%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">点击可跳过</div>
                </div>
            </div>
        `;
    }

    /**
     * 显示自动切换倒计时
     */
    showAutoNextCountdown(isCorrect = true) {
        // 清除之前的倒计时
        this.clearAutoNextCountdown();

        const countdownDuration = isCorrect ? 1500 : 5000; // 答对1.5秒，答错5秒
        const updateInterval = 100; // 每100ms更新一次
        let remainingTime = countdownDuration;

        const countdownText = document.getElementById('countdownText');
        const countdownBar = document.getElementById('countdownBar');

        const updateCountdown = () => {
            remainingTime -= updateInterval;
            const progress = (remainingTime / countdownDuration) * 100;
            const seconds = (remainingTime / 1000).toFixed(1);

            if (countdownText) {
                countdownText.textContent = `${seconds}s`;
            }
            if (countdownBar) {
                countdownBar.style.width = `${Math.max(0, progress)}%`;
            }

            if (remainingTime <= 0) {
                this.countdownTimer = null;
                this.nextQuestion();
            } else {
                this.countdownTimer = setTimeout(updateCountdown, updateInterval);
            }
        };

        this.countdownTimer = setTimeout(updateCountdown, updateInterval);
    }

    /**
     * 清除自动切换倒计时
     */
    clearAutoNextCountdown() {
        if (this.countdownTimer) {
            clearTimeout(this.countdownTimer);
            this.countdownTimer = null;
        }
    }

    /**
     * 清除结果显示
     */
    clearResult() {
        document.getElementById('resultArea').innerHTML = '';
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        document.getElementById('correctCount').textContent = this.correctCount;
        document.getElementById('wrongCount').textContent = this.wrongCount;
        
        const total = this.correctCount + this.wrongCount;
        const accuracy = total > 0 ? Math.round((this.correctCount / total) * 100) : 0;
        document.getElementById('accuracyRate').textContent = `${accuracy}%`;
    }

    /**
     * 更新进度
     */
    updateProgress() {
        const total = this.words.length;
        const current = this.usedIndexes.length;
        const progress = total > 0 ? (current / total) * 100 : 0;
        
        document.getElementById('progressText').textContent = `${current}/${total}`;
        document.getElementById('progressBar').style.width = `${progress}%`;
    }

    /**
     * 完成测验
     */
    async completeQuiz() {
        const totalTime = Date.now() - this.startTime;
        
        // 保存学习记录
        try {
            await wordDB.saveStudyRecord(1, this.currentMode, this.correctCount, this.wrongCount, totalTime);
        } catch (error) {
            console.error('保存学习记录失败:', error);
        }
        
        // 显示完成界面
        this.showCompletionScreen();
    }

    /**
     * 显示完成界面
     */
    showCompletionScreen() {
        document.getElementById('quizContainer').classList.add('hidden');
        document.getElementById('completionScreen').classList.remove('hidden');
        
        const total = this.correctCount + this.wrongCount;
        const accuracy = total > 0 ? Math.round((this.correctCount / total) * 100) : 0;
        
        document.getElementById('finalCorrect').textContent = this.correctCount;
        document.getElementById('finalWrong').textContent = this.wrongCount;
        document.getElementById('finalAccuracy').textContent = `${accuracy}%`;
        
        // 如果没有错题，隐藏复习错题按钮
        if (this.wrongWords.length === 0) {
            document.getElementById('reviewErrorsBtn').classList.add('hidden');
        } else {
            document.getElementById('reviewErrorsBtn').classList.remove('hidden');
        }
    }

    /**
     * 重新开始测验
     */
    restartQuiz() {
        if (this.currentMode === 'spelling') {
            this.startSpellingMode();
        } else if (this.currentMode === 'typing') {
            this.startTypingMode();
        } else if (this.currentMode === 'choice') {
            this.startChoiceMode();
        } else if (this.currentMode === 'review') {
            this.startReviewMode();
        } else if (this.currentMode === 'spencer') {
            this.startSpencerMode();
        }
    }

    /**
     * 返回主菜单
     */
    backToMenu() {
        // 清除倒计时
        this.clearAutoNextCountdown();

        // 停止语音播放
        this.stopCurrentAudio();

        document.getElementById('quizContainer').classList.add('hidden');
        document.getElementById('completionScreen').classList.add('hidden');
        document.getElementById('reviewSelectionScreen').classList.add('hidden');
        document.getElementById('modeSelection').classList.remove('hidden');
        document.getElementById('wordlistManager').classList.remove('hidden');
    }

    /**
     * 处理键盘事件
     */
    handleKeyPress(event) {
        if (document.getElementById('quizContainer').classList.contains('hidden')) return;

        if (event.key === 'Enter') {
            if (!document.getElementById('submitBtn').classList.contains('hidden')) {
                this.submitAnswer();
            }
            // 移除手动按Enter切换下一题的功能，因为现在是自动切换
        }
    }

    /**
     * 显示斯宾塞复习信息
     */
    showSpencerInfo() {
        const infoEl = document.createElement('div');
        infoEl.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4';
        infoEl.innerHTML = `
            <div class="flex items-center mb-2">
                <i class="fas fa-brain text-blue-600 mr-2"></i>
                <span class="font-semibold text-blue-800">斯宾塞间隔重复复习</span>
            </div>
            <div class="text-sm text-blue-700">
                根据记忆曲线智能安排复习，提高长期记忆效果
            </div>
        `;

        const quizContainer = document.getElementById('quizContainer');
        const progressSection = quizContainer.querySelector('.mb-6');
        progressSection.after(infoEl);
    }

    /**
     * 显示斯宾塞复习统计
     */
    showSpencerStats(stats) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
                <div class="text-center mb-6">
                    <i class="fas fa-brain text-6xl text-blue-500 mb-4"></i>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">斯宾塞复习统计</h3>
                    <p class="text-gray-600">基于间隔重复算法的智能复习</p>
                </div>

                <div class="space-y-4 mb-6">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span class="text-gray-700">总错题数</span>
                        <span class="font-bold text-blue-600">${stats.total}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                        <span class="text-gray-700">需要复习</span>
                        <span class="font-bold text-yellow-600">${stats.needReview}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span class="text-gray-700">学习中</span>
                        <span class="font-bold text-blue-600">${stats.learning}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span class="text-gray-700">已掌握</span>
                        <span class="font-bold text-green-600">${stats.mastered}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                        <span class="text-gray-700">困难单词</span>
                        <span class="font-bold text-red-600">${stats.difficult}</span>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-semibold mb-3">掌握程度分布</h4>
                    <div class="space-y-2">
                        ${stats.byMasteryLevel.map((count, level) => `
                            <div class="flex items-center">
                                <span class="w-16 text-sm text-gray-600">级别${level}</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                                    <div class="bg-gradient-to-r from-red-400 to-green-400 h-2 rounded-full"
                                         style="width: ${stats.total > 0 ? (count / stats.total * 100) : 0}%"></div>
                                </div>
                                <span class="w-8 text-sm font-medium">${count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                ${stats.needReview > 0 ? `
                    <button onclick="quizManager.startSpencerMode(); this.closest('.fixed').remove();"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium mb-3">
                        开始复习 (${stats.needReview} 个单词)
                    </button>
                ` : `
                    <div class="text-center p-4 bg-green-50 rounded-lg mb-3">
                        <i class="fas fa-check-circle text-green-500 text-2xl mb-2"></i>
                        <p class="text-green-700 font-medium">今日无需复习</p>
                        <p class="text-green-600 text-sm">明天再来看看吧！</p>
                    </div>
                `}

                <button onclick="this.closest('.fixed').remove();"
                        class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-lg font-medium">
                    关闭
                </button>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 播放单词语音
     */
    playWordAudio(word) {
        if (!this.speechSupported || !this.speechSettings.enabled) {
            console.warn('语音功能不可用');
            return;
        }

        // 检查是否正在播放相同的单词
        if (this.currentUtterance && this.currentUtterance.text === word && this.speechSynthesis.speaking) {
            console.log('相同单词正在播放，跳过');
            return;
        }

        // 温和地停止当前播放的语音
        this.stopCurrentAudio();

        // 等待一小段时间确保之前的语音完全停止
        setTimeout(() => {
            // 创建新的语音实例
            this.currentUtterance = new SpeechSynthesisUtterance(word);

            // 设置语音参数
            this.currentUtterance.rate = this.speechSettings.rate || 0.8;
            this.currentUtterance.pitch = this.speechSettings.pitch || 1;
            this.currentUtterance.volume = this.speechSettings.volume || 0.8;
            this.currentUtterance.lang = this.speechSettings.lang || 'en-US';

            // 设置语音
            if (this.selectedVoice) {
                this.currentUtterance.voice = this.selectedVoice;
            }

            // 添加事件监听
            this.currentUtterance.onstart = () => {
                console.log('语音播放开始:', word);
                this.updateAudioButtonState(true);
            };

            this.currentUtterance.onend = () => {
                console.log('语音播放结束:', word);
                this.updateAudioButtonState(false);
                this.currentUtterance = null;
            };

            this.currentUtterance.onerror = (event) => {
                // 只在非正常中断时显示错误
                if (event.error !== 'canceled' && event.error !== 'interrupted') {
                    console.error('语音播放错误:', event.error);
                    wordlistManager.showMessage('语音播放失败，请检查浏览器设置', 'error');
                } else {
                    console.log('语音播放被中断:', event.error);
                }
                this.updateAudioButtonState(false);
                this.currentUtterance = null;
            };

            // 播放语音
            try {
                this.speechSynthesis.speak(this.currentUtterance);
                console.log('开始播放语音:', word);
            } catch (error) {
                console.error('语音播放失败:', error);
                this.updateAudioButtonState(false);
                this.currentUtterance = null;
            }
        }, 100);
    }

    /**
     * 停止当前语音播放
     */
    stopCurrentAudio() {
        if (this.speechSynthesis.speaking || this.speechSynthesis.pending) {
            try {
                this.speechSynthesis.cancel();
                console.log('语音播放已停止');
            } catch (error) {
                console.warn('停止语音播放时出错:', error);
            }
        }
        this.currentUtterance = null;
        this.updateAudioButtonState(false);
    }

    /**
     * 处理语音按钮点击
     */
    handleAudioButtonClick() {
        if (!this.currentWord) return;

        // 如果正在播放，则停止
        if (this.speechSynthesis.speaking) {
            this.stopCurrentAudio();
        } else {
            // 否则开始播放
            this.playWordAudio(this.currentWord.english);
        }
    }

    /**
     * 更新语音按钮状态
     */
    updateAudioButtonState(isPlaying) {
        const audioBtn = document.getElementById('playAudioBtn');
        if (!audioBtn) return;

        if (isPlaying) {
            audioBtn.innerHTML = '<i class="fas fa-stop"></i>';
            audioBtn.title = '停止播放';
            audioBtn.classList.add('bg-red-500', 'hover:bg-red-600', 'playing');
            audioBtn.classList.remove('bg-blue-500', 'hover:bg-blue-600');
        } else {
            audioBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
            audioBtn.title = '播放发音';
            audioBtn.classList.add('bg-blue-500', 'hover:bg-blue-600');
            audioBtn.classList.remove('bg-red-500', 'hover:bg-red-600', 'playing');
        }
    }

    /**
     * 调度自动播放
     */
    scheduleAutoPlay() {
        // 检查是否已经有用户交互
        if (!this.hasUserInteracted) {
            // 如果没有用户交互，等待用户点击或按键
            this.waitForUserInteraction();
        } else {
            // 如果已经有用户交互，直接播放
            setTimeout(() => {
                this.playWordAudio(this.currentWord.english);
            }, 500);
        }
    }

    /**
     * 等待用户交互
     */
    waitForUserInteraction() {
        if (this.hasUserInteracted) return;

        // 显示提示信息
        const audioBtn = document.getElementById('playAudioBtn');
        if (audioBtn) {
            audioBtn.classList.add('pulse');
            audioBtn.title = '点击播放发音（浏览器需要用户交互才能播放语音）';
        }

        const handleInteraction = () => {
            this.hasUserInteracted = true;
            document.removeEventListener('click', handleInteraction);
            document.removeEventListener('keydown', handleInteraction);

            // 移除提示效果
            if (audioBtn) {
                audioBtn.classList.remove('pulse');
                audioBtn.title = '播放发音';
            }

            // 用户交互后播放语音
            if (this.speechSettings.enabled && this.currentWord) {
                setTimeout(() => {
                    this.playWordAudio(this.currentWord.english);
                }, 200);
            }
        };

        document.addEventListener('click', handleInteraction, { once: true });
        document.addEventListener('keydown', handleInteraction, { once: true });
    }

    /**
     * 切换语音设置
     */
    toggleSpeechSettings() {
        this.speechSettings.enabled = !this.speechSettings.enabled;

        // 保存设置到本地存储
        localStorage.setItem('speechSettings', JSON.stringify(this.speechSettings));

        // 显示提示
        const status = this.speechSettings.enabled ? '已启用' : '已禁用';
        wordlistManager.showMessage(`语音播放${status}`, 'info');
    }

    /**
     * 打乱数组
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}

// 创建全局测验管理器实例
window.quizManager = new QuizManager();
