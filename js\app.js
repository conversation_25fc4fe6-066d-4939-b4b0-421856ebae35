/**
 * 主应用文件
 * 负责应用初始化和全局功能协调
 */

class WordLearnerApp {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 检查用户认证
            if (!window.authManager || !window.authManager.isAuthenticated()) {
                console.log('用户未认证，跳转到登录页');
                window.location.href = '/';
                return;
            }

            // 显示加载状态
            this.showLoadingState();

            // 初始化API管理器
            if (window.apiManager) {
                const token = localStorage.getItem('auth_token');
                window.apiManager.setToken(token);
            }

            // 初始化数据库
            await wordDB.init();

            // 初始化用户界面
            if (window.authManager) {
                window.authManager.updateUserInterface();
            }

            // 初始化词表管理器
            await wordlistManager.refreshWordlistSelect();

            // 初始化事件监听器
            this.initGlobalEventListeners();

            // 隐藏加载状态
            this.hideLoadingState();

            // 检查是否有默认词表
            await this.loadDefaultWordlist();

            this.isInitialized = true;
            console.log('应用初始化完成');

        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showErrorState('应用初始化失败，请刷新页面重试');
        }
    }

    /**
     * 初始化全局事件监听器
     */
    initGlobalEventListeners() {
        // 统计按钮
        document.getElementById('statsBtn').addEventListener('click', () => this.showStats());
        
        // 设置按钮
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettings());
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleGlobalKeyPress(e));
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const loadingEl = document.createElement('div');
        loadingEl.id = 'loadingScreen';
        loadingEl.className = 'fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50';
        loadingEl.innerHTML = `
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">正在初始化应用...</p>
            </div>
        `;
        document.body.appendChild(loadingEl);
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        const loadingEl = document.getElementById('loadingScreen');
        if (loadingEl) {
            loadingEl.remove();
        }
    }

    /**
     * 显示错误状态
     */
    showErrorState(message) {
        const errorEl = document.createElement('div');
        errorEl.className = 'fixed inset-0 bg-red-50 flex items-center justify-center z-50';
        errorEl.innerHTML = `
            <div class="text-center p-8">
                <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-4"></i>
                <h2 class="text-2xl font-bold text-red-800 mb-4">出现错误</h2>
                <p class="text-red-600 mb-6">${message}</p>
                <button onclick="location.reload()" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg">
                    刷新页面
                </button>
            </div>
        `;
        document.body.appendChild(errorEl);
    }

    /**
     * 加载默认词表
     */
    async loadDefaultWordlist() {
        try {
            const wordlists = await wordDB.getAllWordlists();
            if (wordlists.length > 0) {
                // 加载最近使用的词表
                const latestWordlist = wordlists.sort((a, b) => 
                    new Date(b.updatedAt) - new Date(a.updatedAt)
                )[0];
                
                document.getElementById('wordlistSelect').value = latestWordlist.name;
                await wordlistManager.loadWordlist(latestWordlist.name);
            } else {
                // 如果没有词表，提供示例词表
                this.loadSampleWordlist();
            }
        } catch (error) {
            console.error('加载默认词表失败:', error);
        }
    }

    /**
     * 加载示例词表
     */
    loadSampleWordlist() {
        const sampleWords = `苹果 apple
香蕉 banana
橙子 orange
葡萄 grape
草莓 strawberry
西瓜 watermelon
桃子 peach
梨子 pear
樱桃 cherry
菠萝 pineapple`;

        document.getElementById('wordlistName').value = '水果词汇';
        document.getElementById('wordlistContent').value = sampleWords;
        wordlistManager.updateWordCount();
    }

    /**
     * 显示统计信息
     */
    async showStats() {
        try {
            const records = await wordDB.getStudyRecords();
            const wrongWords = await wordDB.getWrongWords();
            
            this.showModal('学习统计', this.generateStatsHTML(records, wrongWords));
        } catch (error) {
            console.error('获取统计信息失败:', error);
            wordlistManager.showMessage('获取统计信息失败', 'error');
        }
    }

    /**
     * 生成统计信息HTML
     */
    generateStatsHTML(records, wrongWords) {
        const totalSessions = records.length;
        const totalQuestions = records.reduce((sum, record) => sum + record.total, 0);
        const totalCorrect = records.reduce((sum, record) => sum + record.correct, 0);
        const averageAccuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
        
        return `
            <div class="space-y-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600">${totalSessions}</div>
                        <div class="text-sm text-gray-600">学习次数</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600">${totalQuestions}</div>
                        <div class="text-sm text-gray-600">总题数</div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-yellow-600">${averageAccuracy}%</div>
                        <div class="text-sm text-gray-600">平均正确率</div>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-red-600">${wrongWords.length}</div>
                        <div class="text-sm text-gray-600">错题数量</div>
                    </div>
                </div>
                
                ${records.length > 0 ? `
                    <div>
                        <h4 class="font-semibold mb-3">最近学习记录</h4>
                        <div class="space-y-2 max-h-60 overflow-y-auto">
                            ${records.slice(0, 10).map(record => `
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                                    <div>
                                        <span class="font-medium">${record.mode === 'spelling' ? '拼写模式' : record.mode === 'choice' ? '选择题' : '错题复习'}</span>
                                        <span class="text-sm text-gray-500 ml-2">${new Date(record.date).toLocaleDateString()}</span>
                                    </div>
                                    <div class="text-sm">
                                        <span class="text-green-600">${record.correct}对</span> / 
                                        <span class="text-red-600">${record.wrong}错</span> / 
                                        <span class="text-blue-600">${record.accuracy.toFixed(1)}%</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : '<p class="text-gray-500 text-center">暂无学习记录</p>'}
            </div>
        `;
    }

    /**
     * 显示设置
     */
    showSettings() {
        const speechSettings = quizManager.speechSettings || { enabled: false };

        const settingsHTML = `
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <div>
                        <label class="font-medium">自动播放发音</label>
                        <div class="text-sm text-gray-500">答题时自动播放单词发音</div>
                    </div>
                    <input type="checkbox" id="autoPlaySound" class="toggle" ${speechSettings.enabled ? 'checked' : ''}>
                </div>
                <div class="flex justify-between items-center">
                    <div>
                        <label class="font-medium">语音速度</label>
                        <div class="text-sm text-gray-500">调整语音播放速度</div>
                    </div>
                    <select id="speechRate" class="px-2 py-1 border rounded">
                        <option value="0.5" ${speechSettings.rate === 0.5 ? 'selected' : ''}>慢速</option>
                        <option value="0.8" ${speechSettings.rate === 0.8 ? 'selected' : ''}>正常</option>
                        <option value="1.0" ${speechSettings.rate === 1.0 ? 'selected' : ''}>快速</option>
                        <option value="1.2" ${speechSettings.rate === 1.2 ? 'selected' : ''}>很快</option>
                    </select>
                </div>
                <div class="flex justify-between items-center">
                    <div>
                        <label class="font-medium">语音音量</label>
                        <div class="text-sm text-gray-500">调整语音播放音量</div>
                    </div>
                    <input type="range" id="speechVolume" min="0" max="1" step="0.1" value="${speechSettings.volume || 0.8}" class="w-20">
                </div>
                <div class="flex justify-between items-center">
                    <button onclick="app.testSpeech()" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                        <i class="fas fa-volume-up mr-2"></i>测试语音
                    </button>
                    <button onclick="app.saveSpeechSettings()" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded">
                        <i class="fas fa-save mr-2"></i>保存设置
                    </button>
                </div>
                <hr>
                <div class="space-y-2">
                    <button onclick="app.clearAllData()" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">
                        清空所有数据
                    </button>
                    <button onclick="app.exportAllData()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded">
                        导出所有数据
                    </button>
                </div>
            </div>
        `;

        this.showModal('设置', settingsHTML);
    }

    /**
     * 显示模态框
     */
    showModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">${title}</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div>${content}</div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 清空所有数据
     */
    async clearAllData() {
        if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) return;
        
        try {
            await wordDB.clearAllWrongWords();
            // 这里可以添加更多清理操作
            wordlistManager.showMessage('数据清空成功', 'success');
            location.reload();
        } catch (error) {
            console.error('清空数据失败:', error);
            wordlistManager.showMessage('清空数据失败', 'error');
        }
    }

    /**
     * 保存语音设置
     */
    saveSpeechSettings() {
        const enabled = document.getElementById('autoPlaySound').checked;
        const rate = parseFloat(document.getElementById('speechRate').value);
        const volume = parseFloat(document.getElementById('speechVolume').value);

        if (quizManager.speechSettings) {
            quizManager.speechSettings.enabled = enabled;
            quizManager.speechSettings.rate = rate;
            quizManager.speechSettings.volume = volume;

            // 保存到本地存储
            localStorage.setItem('speechSettings', JSON.stringify(quizManager.speechSettings));

            wordlistManager.showMessage('语音设置已保存', 'success');
        }
    }

    /**
     * 测试语音
     */
    testSpeech() {
        if (quizManager.speechSupported) {
            // 临时应用当前设置
            const rate = parseFloat(document.getElementById('speechRate').value);
            const volume = parseFloat(document.getElementById('speechVolume').value);

            const utterance = new SpeechSynthesisUtterance('Hello, this is a test');
            utterance.rate = rate;
            utterance.volume = volume;
            utterance.lang = 'en-US';

            if (quizManager.selectedVoice) {
                utterance.voice = quizManager.selectedVoice;
            }

            speechSynthesis.speak(utterance);
        } else {
            wordlistManager.showMessage('浏览器不支持语音功能', 'error');
        }
    }

    /**
     * 导出所有数据
     */
    async exportAllData() {
        try {
            const data = await wordDB.exportAllData();
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `word-learner-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            wordlistManager.showMessage('数据导出成功', 'success');
        } catch (error) {
            console.error('导出数据失败:', error);
            wordlistManager.showMessage('导出数据失败', 'error');
        }
    }

    /**
     * 处理全局键盘事件
     */
    handleGlobalKeyPress(event) {
        // Ctrl/Cmd + S 保存词表
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
            event.preventDefault();
            if (!document.getElementById('quizContainer').classList.contains('hidden')) return;
            wordlistManager.saveWordlist();
        }
        
        // Esc 键返回主页
        if (event.key === 'Escape') {
            if (!document.getElementById('quizContainer').classList.contains('hidden')) {
                quizManager.backToMenu();
            }
        }
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停计时等
            console.log('页面隐藏');
        } else {
            // 页面显示时恢复
            console.log('页面显示');
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 响应式调整
        console.log('窗口大小变化');
    }
}

// 创建全局应用实例
window.app = new WordLearnerApp();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面加载完成，应用正在初始化...');

    // 检查当前页面是否是应用主页面
    if (window.location.pathname === '/app') {
        // 初始化认证管理器
        if (window.authManager) {
            window.authManager.init();
        }
    }
});
