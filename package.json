{"name": "word-learner-ocr-proxy", "version": "1.0.0", "description": "百度OCR代理服务器，用于解决CORS跨域问题", "main": "baidu-ocr-proxy.js", "scripts": {"start": "node baidu-ocr-proxy.js", "dev": "nodemon baidu-ocr-proxy.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^2.6.7"}, "devDependencies": {"nodemon": "^2.0.20"}, "keywords": ["ocr", "baidu", "proxy", "cors"], "author": "Word Learner", "license": "MIT"}